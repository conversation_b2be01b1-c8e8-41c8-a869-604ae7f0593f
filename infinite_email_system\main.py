"""
无限邮箱系统主程序入口
"""

import asyncio
import tkinter as tk
from tkinter import messagebox
import sys
import os
from pathlib import Path
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from config.settings import ConfigManager
    from config.database import DatabaseManager
    from core.domain_manager import DomainManager
    from core.email_generator import EmailGenerator
    from core.cloudflare_api import CloudflareEmailAPI
    from core.email_receiver import EmailReceiver
    from utils.logger import setup_logger
    from utils.crypto import CryptoManager
    from utils.exceptions import EmailSystemException, ConfigurationError
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保所有依赖包已正确安装")
    sys.exit(1)


class InfiniteEmailSystem:
    """无限邮箱系统主类"""
    
    def __init__(self):
        self.config = None
        self.db = None
        self.crypto = None
        self.logger = None
        
        # 核心组件
        self.domain_manager = None
        self.email_generator = None
        self.cloudflare_api = None
        self.email_receiver = None
        
        # UI组件
        self.main_window = None
        self.root = None
        
        # 初始化标志
        self.initialized = False
    
    async def initialize(self) -> bool:
        """初始化系统"""
        try:
            print("正在初始化无限邮箱系统...")
            
            # 1. 设置日志
            self.logger = setup_logger("infinite_email_system", level="INFO")
            self.logger.info("Starting Infinite Email System initialization")
            
            # 2. 初始化加密管理器
            self.crypto = CryptoManager()
            if not self.crypto.verify_key():
                self.logger.warning("Encryption key verification failed, generating new key")
                self.crypto.generate_new_key()
            
            # 3. 初始化配置管理器
            self.config = ConfigManager()
            
            # 验证配置
            config_errors = self.config.validate_config()
            if config_errors:
                self.logger.warning(f"Configuration issues found: {config_errors}")
                # 对于非关键错误，继续运行
            
            # 4. 初始化数据库
            db_path = self.config.get('database.path', 'data/emails.db')
            self.db = DatabaseManager(db_path)
            await self.db.initialize()
            
            # 5. 初始化核心组件
            self.domain_manager = DomainManager()
            self.email_generator = EmailGenerator()
            
            # 6. 初始化Cloudflare API（如果配置了）
            api_token = self.config.get_encrypted('cloudflare.api_token')
            if api_token:
                try:
                    self.cloudflare_api = CloudflareEmailAPI(api_token)
                    # 测试连接
                    async with self.cloudflare_api as api:
                        connection_ok = await api.test_connection()
                        if connection_ok:
                            self.logger.info("Cloudflare API connection successful")
                        else:
                            self.logger.warning("Cloudflare API connection failed")
                except Exception as e:
                    self.logger.warning(f"Failed to initialize Cloudflare API: {e}")
                    self.cloudflare_api = None
            else:
                self.logger.info("Cloudflare API token not configured")
            
            # 7. 初始化邮件接收器
            self.email_receiver = EmailReceiver()
            
            self.initialized = True
            self.logger.info("System initialization completed successfully")
            print("系统初始化完成！")
            return True
            
        except Exception as e:
            error_msg = f"System initialization failed: {e}"
            if self.logger:
                self.logger.error(error_msg)
            else:
                print(f"错误: {error_msg}")
            return False
    
    def create_gui(self):
        """创建图形用户界面"""
        try:
            # 创建主窗口
            self.root = tk.Tk()
            self.root.title(f"{self.config.get('app.name', 'Infinite Email System')} v{self.config.get('app.version', '1.0.0')}")
            
            # 设置窗口大小和位置
            window_size = self.config.get('ui.window_size', '1200x800')
            self.root.geometry(window_size)
            self.root.minsize(800, 600)
            
            # 设置应用图标
            try:
                icon_path = project_root / "resources" / "icons" / "app.ico"
                if icon_path.exists():
                    self.root.iconbitmap(str(icon_path))
            except Exception as e:
                self.logger.warning(f"Failed to set application icon: {e}")
            
            # 创建简单的临时界面
            self._create_temporary_ui()
            
            self.logger.info("GUI created successfully")
            
        except Exception as e:
            raise EmailSystemException(f"Failed to create GUI: {e}")
    
    def _create_temporary_ui(self):
        """创建临时用户界面"""
        # 主框架
        main_frame = tk.Frame(self.root, padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = tk.Label(
            main_frame, 
            text="无限邮箱系统", 
            font=("Arial", 24, "bold"),
            fg="#2c3e50"
        )
        title_label.pack(pady=(0, 20))
        
        # 状态信息
        status_frame = tk.LabelFrame(main_frame, text="系统状态", padx=10, pady=10)
        status_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 系统状态
        status_text = "✅ 系统已初始化" if self.initialized else "❌ 系统未初始化"
        tk.Label(status_frame, text=f"状态: {status_text}").pack(anchor=tk.W)
        
        # 数据库状态
        db_status = "✅ 已连接" if self.db and self.db._initialized else "❌ 未连接"
        tk.Label(status_frame, text=f"数据库: {db_status}").pack(anchor=tk.W)
        
        # Cloudflare API状态
        cf_status = "✅ 已配置" if self.cloudflare_api else "⚠️ 未配置"
        tk.Label(status_frame, text=f"Cloudflare API: {cf_status}").pack(anchor=tk.W)
        
        # 功能按钮
        button_frame = tk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 生成分类邮箱按钮
        tk.Button(
            button_frame,
            text="生成分类邮箱",
            command=self._generate_category_email,
            bg="#3498db",
            fg="white",
            font=("Arial", 12),
            padx=20,
            pady=10
        ).pack(side=tk.LEFT, padx=(0, 10))
        
        # 生成随机邮箱按钮
        tk.Button(
            button_frame,
            text="生成随机邮箱",
            command=self._generate_random_email,
            bg="#2ecc71",
            fg="white",
            font=("Arial", 12),
            padx=20,
            pady=10
        ).pack(side=tk.LEFT, padx=(0, 10))
        
        # 查看统计按钮
        tk.Button(
            button_frame,
            text="查看统计",
            command=self._show_statistics,
            bg="#f39c12",
            fg="white",
            font=("Arial", 12),
            padx=20,
            pady=10
        ).pack(side=tk.LEFT, padx=(0, 10))
        
        # 生成子域名按钮
        tk.Button(
            button_frame,
            text="生成子域名",
            command=self._generate_subdomain,
            bg="#9b59b6",
            fg="white",
            font=("Arial", 12),
            padx=20,
            pady=10
        ).pack(side=tk.LEFT, padx=(0, 10))

        # 设置按钮
        tk.Button(
            button_frame,
            text="设置",
            command=self._show_settings,
            bg="#95a5a6",
            fg="white",
            font=("Arial", 12),
            padx=20,
            pady=10
        ).pack(side=tk.LEFT)
        
        # 输出区域
        output_frame = tk.LabelFrame(main_frame, text="输出", padx=10, pady=10)
        output_frame.pack(fill=tk.BOTH, expand=True)
        
        # 文本框和滚动条
        text_frame = tk.Frame(output_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        self.output_text = tk.Text(text_frame, wrap=tk.WORD, font=("Consolas", 10))
        scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.output_text.yview)
        self.output_text.configure(yscrollcommand=scrollbar.set)
        
        self.output_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 清空按钮
        tk.Button(
            output_frame,
            text="清空输出",
            command=lambda: self.output_text.delete(1.0, tk.END),
            bg="#e74c3c",
            fg="white"
        ).pack(pady=(10, 0))
        
        # 显示欢迎信息
        domain_name = self.config.get('email.domain', 'cfish22.dpdns.org')
        forward_email = self.config.get('email.default_forward_to', '<EMAIL>')

        welcome_msg = f"""欢迎使用无限邮箱系统！

当前配置：
• 主域名: {domain_name}
• 转发邮箱: {forward_email}
• 系统状态: {'✅ 已就绪' if self.initialized else '⚠️ 未完全初始化'}

功能特性：
• 基于 {domain_name} 生成无限邮箱地址
• 自动转发到 {forward_email}
• Cloudflare 邮件路由支持
• 批量管理和监控

使用说明：
1. 点击"生成随机邮箱"创建新的邮箱地址
2. 所有邮件将自动转发到您的主邮箱
3. 查看统计了解系统使用情况
4. 在设置中可修改转发邮箱

系统已配置完成，可以开始使用！
"""
        self.output_text.insert(tk.END, welcome_msg)
    
    def _generate_category_email(self):
        """生成分类邮箱"""
        try:
            if not self.email_generator:
                self.output_text.insert(tk.END, "错误：邮箱生成器未初始化\n")
                return

            # 获取主域名
            main_domain = self.config.get('email.domain', 'cfish22.dpdns.org')

            # 预定义的分类
            categories = ['shopping', 'work', 'social', 'newsletter', 'temp', 'test', 'support', 'finance']

            import random
            category = random.choice(categories)
            random_suffix = self.email_generator.generate_random_prefix(length=4, pattern='mixed')

            email = f"{category}-{random_suffix}@{main_domain}"

            self.output_text.insert(tk.END, f"生成的分类邮箱: {email}\n")
            self.output_text.insert(tk.END, f"分类: {category} (便于识别邮件来源)\n")

            # 在新线程中保存到数据库
            def save_email():
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    loop.run_until_complete(self.db.save_email(email, main_domain, f"{category}-{random_suffix}"))
                    self.root.after(0, lambda: self.output_text.insert(tk.END, f"邮箱已保存到数据库\n"))
                except Exception as e:
                    self.root.after(0, lambda: self.output_text.insert(tk.END, f"保存邮箱失败: {e}\n"))
                finally:
                    loop.close()

            import threading
            threading.Thread(target=save_email, daemon=True).start()

        except Exception as e:
            self.output_text.insert(tk.END, f"生成分类邮箱时出错: {e}\n")

    def _generate_subdomain(self):
        """生成真正的子域名"""
        try:
            if not self.email_generator:
                self.output_text.insert(tk.END, "错误：邮箱生成器未初始化\n")
                return

            # 获取主域名
            main_domain = self.config.get('email.domain', 'cfish22.dpdns.org')

            # 生成子域名前缀
            prefix = self.email_generator.generate_random_prefix(length=6, pattern='mixed')
            subdomain = f"{prefix}.{main_domain}"

            self.output_text.insert(tk.END, f"生成的子域名: {subdomain}\n")
            self.output_text.insert(tk.END, f"用途示例:\n")
            self.output_text.insert(tk.END, f"  • 邮箱: anything@{subdomain}\n")
            self.output_text.insert(tk.END, f"  • 网站: https://{subdomain}\n")
            self.output_text.insert(tk.END, f"  • API: api.{subdomain}\n")
            self.output_text.insert(tk.END, f"注意：需要在 Cloudflare 中配置通配符 DNS 记录\n\n")

        except Exception as e:
            self.output_text.insert(tk.END, f"生成子域名时出错: {e}\n")
    
    def _generate_random_email(self):
        """生成随机邮箱"""
        try:
            if not self.email_generator:
                self.output_text.insert(tk.END, "错误：邮箱生成器未初始化\n")
                return

            # 获取配置的域名
            domain = self.config.get('email.domain', 'cfish22.dpdns.org')

            # 生成随机邮箱
            prefix = self.email_generator.generate_random_prefix()
            email = f"{prefix}@{domain}"

            self.output_text.insert(tk.END, f"生成的邮箱: {email}\n")

            # 在新线程中保存到数据库
            def save_email():
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    loop.run_until_complete(self.db.save_email(email, domain, prefix))
                    self.root.after(0, lambda: self.output_text.insert(tk.END, f"邮箱已保存到数据库\n"))
                except Exception as e:
                    self.root.after(0, lambda: self.output_text.insert(tk.END, f"保存邮箱失败: {e}\n"))
                finally:
                    loop.close()

            import threading
            threading.Thread(target=save_email, daemon=True).start()

        except Exception as e:
            self.output_text.insert(tk.END, f"生成邮箱时出错: {e}\n")
    
    def _show_statistics(self):
        """显示统计信息"""
        try:
            if not self.db:
                self.output_text.insert(tk.END, "错误：数据库未初始化\n")
                return
            
            def run_async():
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    stats = loop.run_until_complete(self.db.get_statistics())
                    stats_text = f"""
统计信息：
• 活跃域名: {stats.get('active_domains', 0)}
• 活跃邮箱: {stats.get('active_emails', 0)}
• 今日创建邮箱: {stats.get('emails_created_today', 0)}
• 总使用次数: {stats.get('total_usage', 0)}
"""
                    self.root.after(0, lambda: self.output_text.insert(tk.END, stats_text))
                except Exception as e:
                    self.root.after(0, lambda: self.output_text.insert(tk.END, f"获取统计信息失败: {e}\n"))
                finally:
                    loop.close()
            
            import threading
            threading.Thread(target=run_async, daemon=True).start()
            
        except Exception as e:
            self.output_text.insert(tk.END, f"显示统计信息时出错: {e}\n")
    
    def _show_settings(self):
        """显示设置窗口"""
        messagebox.showinfo("设置", "设置功能正在开发中...")
    
    def run(self):
        """运行应用程序"""
        try:
            # 异步初始化
            async def async_init():
                return await self.initialize()
            
            # 运行初始化
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                success = loop.run_until_complete(async_init())
                if not success:
                    messagebox.showerror("错误", "系统初始化失败，请检查配置和日志")
                    return
            finally:
                loop.close()
            
            # 创建GUI
            self.create_gui()
            
            # 启动GUI主循环
            self.logger.info("Starting GUI main loop")
            self.root.mainloop()
            
        except KeyboardInterrupt:
            self.logger.info("Application interrupted by user")
        except Exception as e:
            error_msg = f"Application error: {e}"
            self.logger.error(error_msg) if self.logger else print(error_msg)
            messagebox.showerror("错误", error_msg)
        finally:
            self.cleanup()
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.logger:
                self.logger.info("Cleaning up resources...")
            
            # 清理异步组件
            async def async_cleanup():
                if self.db:
                    await self.db.close()
                if self.domain_manager:
                    await self.domain_manager.cleanup()
                if self.email_receiver:
                    await self.email_receiver.disconnect()
            
            # 运行清理
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(async_cleanup())
                loop.close()
            except:
                pass
            
            if self.logger:
                self.logger.info("Resource cleanup completed")
                
        except Exception as e:
            print(f"Error during cleanup: {e}")


def main():
    """主函数"""
    try:
        # 检查Python版本
        if sys.version_info < (3, 8):
            print("错误：需要Python 3.8或更高版本")
            sys.exit(1)
        
        # 创建必要目录
        for directory in ['data', 'logs', 'config', 'resources']:
            Path(directory).mkdir(exist_ok=True)
        
        print("正在启动无限邮箱系统...")
        
        # 启动应用
        app = InfiniteEmailSystem()
        app.run()
        
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
