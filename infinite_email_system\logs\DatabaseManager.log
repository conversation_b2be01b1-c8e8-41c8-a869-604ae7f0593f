2025-07-30 19:27:19 - DatabaseManager - INFO - Database initialized: data\emails.db
2025-07-30 22:10:22 - DatabaseManager - INFO - Database initialized: data\emails.db
2025-07-30 22:14:40 - DatabaseManager - INFO - Database initialized: data\emails.db
2025-07-30 22:17:06 - DatabaseManager - INFO - Database initialized: data\emails.db
2025-07-30 22:19:32 - DatabaseManager - INFO - Database initialized: data\emails.db
2025-07-30 22:21:31 - DatabaseManager - INFO - Database initialized: data\emails.db
2025-07-30 22:22:38 - DatabaseManager - INFO - Database initialized: data\emails.db
2025-07-30 22:22:52 - DatabaseManager - INFO - Database initialized: data\emails.db
2025-07-30 22:22:52 - DatabaseManager - INFO - Database initialized: data\emails.db
2025-07-30 22:22:52 - DatabaseManager - ERROR - Failed to <NAME_EMAIL>: 'DatabaseManager' object has no attribute 'get_domain_id_by_name'
2025-07-30 22:24:29 - DatabaseManager - INFO - Database initialized: data\emails.db
2025-07-30 22:24:41 - DatabaseManager - INFO - Database initialized: data\emails.db
2025-07-30 22:24:41 - DatabaseManager - INFO - Database initialized: data\emails.db
2025-07-30 22:24:41 - DatabaseManager - ERROR - Failed to <NAME_EMAIL>: Failed to add domain: threads can only be started once
2025-07-30 22:26:19 - DatabaseManager - INFO - Database initialized: data\emails.db
2025-07-30 22:26:33 - DatabaseManager - INFO - Database initialized: data\emails.db
2025-07-30 22:26:33 - DatabaseManager - INFO - Database initialized: data\emails.db
2025-07-30 22:26:33 - DatabaseManager - ERROR - Failed to <NAME_EMAIL>: Failed to add domain: 'tld'
2025-07-30 22:27:41 - DatabaseManager - INFO - Database initialized: data\emails.db
2025-07-30 22:27:56 - DatabaseManager - INFO - Database initialized: data\emails.db
2025-07-30 22:27:56 - DatabaseManager - INFO - Database initialized: data\emails.db
2025-07-30 22:33:23 - DatabaseManager - INFO - Database initialized: data\emails.db
2025-07-30 22:33:23 - DatabaseManager - INFO - Database initialized: data\emails.db
2025-07-30 22:35:22 - DatabaseManager - INFO - Database initialized: data\emails.db
2025-07-30 22:35:22 - DatabaseManager - INFO - Database initialized: data\emails.db
2025-07-30 23:00:24 - DatabaseManager - INFO - Database initialized: data\emails.db
